const Base = require('../../common/controller/base.js');
const moment = require('moment');
const _ = require('lodash');
const rp = require('request-promise');
const fs = require('fs');
const http = require("http");
module.exports = class extends Base {
    /**
     * 获取订单列表
     * @return {Promise} []
     */
    async listAction() {
        const showType = this.get('showType');
        const userId = this.getLoginUserId();
        const page = this.get('page');
        const size = this.get('size');
        const currentTime = parseInt(Date.now() / 1000);
        const startTime = this.get('startTime') || '';
        const endTime = this.get('endTime') || '';

        let where = {
            'o.user_id': userId,
            'o.is_delete': 0,
            'o.order_type': ['<', 7],
        }
        if (startTime && endTime) {
            where['o.create_time'] = ['BETWEEN', [startTime, endTime]];
        }

        const statusList = await this.model('order').getOrderStatus(showType);
        if (statusList.length > 0) {
            where['o.order_status'] = ['IN', statusList];
        }
        // 查出订单列表
        const orderList = await this.model('order')
            .alias('o')
            .field('o.*, ct.name as coupon_name')
            .join({
                table: 'coupon_template',
                join: 'left',
                as: 'ct',
                on: ['ct.id', 'o.coupon_template_id']
            })
            .where(where)
            .page(page, size)
            .order('create_time DESC')
            .countSelect();

        const orders = orderList.data;

        if (orders.length === 0) {
            return this.success({ data: [], count: 0 });
        }

        const result = await this.queryOrderFunc(orders, userId, currentTime);
        return this.success({
            data: result,
            count: orderList.count
        });
    }

    /**
     * 订单搜索
     */
    async searchOrderAction() {
        const userId = this.getLoginUserId();
        const currentTime = parseInt(Date.now() / 1000);
        const keywords = this.get('keywords') || '';

        if (think.isEmpty(keywords)) {
            return this.success([]);
        }

        let where = {
            'o.user_id': userId,
            'o.is_delete': 0,
            'o.order_type': ['<', 7],
        }

        // 查出订单列表
        const orders = await this.model('order')
            .alias('o')
            .field('o.*, ct.name as coupon_name')
            .join({
                table: 'coupon_template',
                join: 'left',
                as: 'ct',
                on: ['ct.id', 'o.coupon_template_id']
            })
            .where({
                _complex: {
                    _logic: 'or',
                    'o.order_sn': ['like', `%${keywords}%`],
                    'o.consignee': ['like', `%${keywords}%`],
                    'o.print_info': ['like', `%${keywords}%`]
                },
                ...where
            })
            .order('create_time DESC')
            .select();

        if (orders.length === 0) {
            return this.success([]);
        }

        const result = await this.queryOrderFunc(orders, userId, currentTime);
        return this.success(result);
    }

    async queryOrderFunc(orders, userId, currentTime) {
        const orderIds = orders.map(o => o.id);

        // 查商品一次性查出所有
        const goodsList = await this.model('order_goods')
            .field('order_id,id,product_id,goods_id,list_pic_url,number,goods_name,goods_specifition_name,retail_price,create_time,freight_name')
            .where({
                user_id: userId,
                order_id: ['IN', orderIds],
                is_delete: 0
            })
            .select();

        const goodsMap = _.groupBy(goodsList, 'order_id');

        // 要更新状态的订单
        const expiredOrderIds = [];
        const confirmOrderIds = []; // 需要更新为已完成的订单

        for (const order of orders) {
            const isPending = [101, 801].includes(order.order_status);
            const isConfirm = [301, 302, 303].includes(order.order_status);

            const expiredTime = moment(order.create_time).unix() + 24 * 60 * 60;
            const confirmTime = order.shipping_time + 7 * 24 * 60 * 60;

            if (isPending && expiredTime < currentTime) {
                order.order_status = 102; // 设置为已取消
                expiredOrderIds.push(order.id);
            }

            if (isConfirm && order.shipping_time != 0 && confirmTime < currentTime) {
                order.order_status = 401; // 设置为已完成
                confirmOrderIds.push(order.id);
            }

            // 商品信息
            const orderGoods = goodsMap[order.id] || [];
            order.goodsList = orderGoods;
            order.goodsCount = orderGoods.reduce((sum, item) => sum + item.number, 0);
        }

        // 批量更新已过期订单并处理优惠券退还
        if (expiredOrderIds.length > 0) {
            try {
                await this.transaction('order', async (session) => {
                    // 更新订单状态
                    await this.model('order').db(session).where({ id: ['IN', expiredOrderIds] }).update({ order_status: 102 });

                    // 处理优惠券退还
                    const expiredOrders = orders.filter(order => expiredOrderIds.includes(order.id));
                    for (const order of expiredOrders) {
                        if (order.coupon_code && order.coupon_template_id > 0) {
                            const couponService = this.service('coupon');
                            const refundResult = await couponService.refundCoupon(order.coupon_code, order.user_id);
                            if (!refundResult) {
                                think.logger.error(`订单列表页面取消过期订单时退还优惠券失败，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}`);
                            } else {
                                think.logger.info(`订单列表页面取消过期订单成功退还优惠券，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}`);
                            }
                        }
                    }
                });
            } catch (error) {
                think.logger.error('批量取消过期订单失败:', error.message);
                think.logger.error('错误堆栈:', error.stack);
                think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
                // 即使失败也继续执行，避免影响订单列表显示
            }
        }
        if (confirmOrderIds.length > 0) {
            await this.model('order').where({ id: ['IN', confirmOrderIds] }).update({ order_status: 401 });
        }

        // 并发获取订单状态文本、操作按钮和运费模板标题
        await Promise.all(
            orders.map(async order => {
                order.order_status_text = await this.model('order').getOrderStatusText(order.order_status);
                order.handleOption = await this.model('order').getOrderHandleOption(order.id, order);

                // 添加运费模板标题 - 从订单商品表中获取
                const orderGoods = await this.model('order_goods').where({
                    order_id: order.id,
                    is_delete: 0
                }).select();

                if (orderGoods.length > 0) {
                    // 从订单商品中获取运费名称并去重
                    const freightNames = [...new Set(orderGoods
                        .map(item => item.freight_name || '默认物流')
                        .filter(name => name && name.trim() !== '')
                    )];

                    if (freightNames.length === 0) {
                        order.freightTemplateTitle = '默认物流';
                    } else if (freightNames.length === 1) {
                        order.freightTemplateTitle = freightNames[0];
                    } else {
                        // 多个不同的运费名称，用逗号分隔
                        order.freightTemplateTitle = freightNames.join(', ');
                    }
                } else {
                    order.freightTemplateTitle = '默认物流';
                }
            })
        );

        return orders;
    }

    // 获得订单数量
    //
    async countAction() {
        const showType = this.get('showType');
        const userId = this.getLoginUserId();;
        let status = [];
        status = await this.model('order').getOrderStatus(showType);
        let is_delete = 0;
        const allCount = await this.model('order').where({
            user_id: userId,
            is_delete: is_delete,
            order_status: ['IN', status]
        }).count('id');
        return this.success({
            allCount: allCount,
        });
    }
    // 获得订单数量状态
    //
    async orderCountAction() {
        const user_id = this.getLoginUserId();
        const startTime = this.get('startTime') || '';
        const endTime = this.get('endTime') || '';

        let timeWhere = {}
        if (startTime && endTime) {
            timeWhere['create_time'] = ['BETWEEN', [startTime, endTime]];
        }
        if (user_id != 0) {
            let toPay = await this.model('order').where({
                user_id: user_id,
                is_delete: 0,
                order_type: ['<', 7],
                order_status: ['IN', '101,801'],
                ...timeWhere
            }).count('id');
            let toDelivery = await this.model('order').where({
                user_id: user_id,
                is_delete: 0,
                order_type: ['<', 7],
                order_status: ['IN', '201,300'],
                ...timeWhere
            }).count('id');
            let toReceive = await this.model('order').where({
                user_id: user_id,
                order_type: ['<', 7],
                is_delete: 0,
                order_status: 301,
                ...timeWhere
            }).count('id');
            let afterSales = await this.model('order').where({
                user_id: user_id,
                order_type: ['<', 7],
                is_delete: 0,
                order_status: ['IN', '202, 203, 204, 205, 206'],
                ...timeWhere
            }).count('id');
            let confirmed = await this.model('order').where({
                user_id: user_id,
                order_type: ['<', 7],
                is_delete: 0,
                order_status: 401,
                ...timeWhere
            }).count('id');
            let newStatus = {
                toPay: toPay,
                toDelivery: toDelivery,
                toReceive: toReceive,
                afterSales: afterSales,
                confirmed: confirmed
            }
            return this.success(newStatus);
        }

    }
    async detailAction() {
        const orderId = this.get('orderId');
        const orderInfo = await this.model('order')
            .alias('o')
            .field('o.*, ct.name as coupon_name')
            .join({
                table: 'coupon_template',
                join: 'left',
                as: 'ct',
                on: ['ct.id', 'o.coupon_template_id']
            }).where({ 'o.id': orderId }).find();
        const currentTime = parseInt(new Date().getTime() / 1000);
        if (think.isEmpty(orderInfo)) {
            return this.fail('订单不存在');
        }
        orderInfo.province_name = await this.model('region').where({
            id: orderInfo.province
        }).getField('name', true);
        orderInfo.city_name = await this.model('region').where({
            id: orderInfo.city
        }).getField('name', true);
        orderInfo.district_name = await this.model('region').where({
            id: orderInfo.district
        }).getField('name', true);
        orderInfo.full_region = orderInfo.province_name + orderInfo.city_name + orderInfo.district_name;
        const orderGoods = await this.model('order_goods')
            .field('*,freight_name')
            .where({
                order_id: orderId,
                is_delete: 0
            }).select();
        var goodsCount = 0;
        for (const gitem of orderGoods) {
            goodsCount += gitem.number;
        }
        // 订单状态的处理
        orderInfo.order_status_text = await this.model('order').getOrderStatusText(orderInfo.order_status);
        if (think.isEmpty(orderInfo.confirm_time)) {
            orderInfo.confirm_time = 0;
        } else orderInfo.confirm_time = moment.unix(orderInfo.confirm_time).format('YYYY-MM-DD HH:mm:ss');
        if (think.isEmpty(orderInfo.dealdone_time)) {
            orderInfo.dealdone_time = 0;
        } else orderInfo.dealdone_time = moment.unix(orderInfo.dealdone_time).format('YYYY-MM-DD HH:mm:ss');
        if (think.isEmpty(orderInfo.pay_time)) {
            orderInfo.pay_time = 0;
        } else orderInfo.pay_time = moment.unix(orderInfo.pay_time).format('YYYY-MM-DD HH:mm:ss');
        if (think.isEmpty(orderInfo.shipping_time)) {
            orderInfo.shipping_time = 0;
        } else {
            orderInfo.confirm_remainTime = orderInfo.shipping_time + 10 * 24 * 60 * 60;
            orderInfo.shipping_time = moment.unix(orderInfo.shipping_time).format('YYYY-MM-DD HH:mm:ss');
        }
        // 订单支付倒计时
        if (orderInfo.order_status === 101 || orderInfo.order_status === 801) {
            orderInfo.final_pay_time = moment(orderInfo.create_time).add(24, 'hours').unix(); //支付倒计时24小时
            if (orderInfo.final_pay_time < currentTime) {
                //超过时间不支付，更新订单状态为取消
                let updateInfo = {
                    order_status: 102
                };

                try {
                    await this.transaction('order', async (session) => {
                        // 更新订单状态
                        await this.model('order').db(session).where({
                            id: orderId
                        }).update(updateInfo);

                        // 如果使用了优惠券，需要退还优惠券
                        if (orderInfo.coupon_code && orderInfo.coupon_template_id > 0) {
                            const couponService = this.service('coupon');
                            const refundResult = await couponService.refundCoupon(orderInfo.coupon_code, orderInfo.user_id);
                            if (!refundResult) {
                                think.logger.error(`订单超时取消时退还优惠券失败，订单ID: ${orderId}, 优惠券码: ${orderInfo.coupon_code}`);
                            } else {
                                think.logger.info(`订单超时取消成功退还优惠券，订单ID: ${orderId}, 优惠券码: ${orderInfo.coupon_code}`);
                            }
                        }
                    });

                    // 更新当前订单信息的状态，以便后续逻辑正确处理
                    orderInfo.order_status = 102;
                } catch (error) {
                    think.logger.error('订单超时取消失败:', error.message);
                    think.logger.error('错误堆栈:', error.stack);
                    think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
                }
            }
        }
        // 订单可操作的选择,删除，支付，收货，评论，退换货
        const handleOption = await this.model('order').getOrderHandleOption(orderId, orderInfo);

        // 添加运费模板标题 - 从订单商品中获取运费名称
        if (orderGoods.length > 0) {
            // 从订单商品中获取运费名称并去重
            const freightNames = [...new Set(orderGoods
                .map(item => item.freight_name || '默认物流')
                .filter(name => name && name.trim() !== '')
            )];

            if (freightNames.length === 0) {
                orderInfo.freightTemplateTitle = '默认物流';
            } else if (freightNames.length === 1) {
                orderInfo.freightTemplateTitle = freightNames[0];
            } else {
                // 多个不同的运费名称，用逗号分隔
                orderInfo.freightTemplateTitle = freightNames.join(', ');
            }
        } else {
            orderInfo.freightTemplateTitle = '默认物流';
        }

        const returnObj = {
            orderInfo: orderInfo,
            orderGoods: orderGoods,
            handleOption: handleOption,
            goodsCount: goodsCount,
            returnExpress: {},
            sendExpress: {},
        }

        if (handleOption.logistics) {
            const expressInfo = await this.model('order_express').where({
                order_id: orderId,
                is_delete: 0
            }).select();
            if (!think.isEmpty(expressInfo)) {
                let sendExpress = [];
                let returnExpress = [];
                for (const item of expressInfo) {
                    if (item.express_type == 1) {
                        const latestExpressInfo = await this.model('order_express').queryExpress100(orderId, orderInfo.mobile, 1);
                        const expressObj = JSON.parse(latestExpressInfo.traces);

                        const express = {
                            request_time: moment.unix(latestExpressInfo.request_time).format('YYYY-MM-DD HH:mm:ss'),
                            is_finish: latestExpressInfo.is_finish,
                            traces: expressObj.data || JSON.parse(latestExpressInfo.traces),
                            logistic_code: latestExpressInfo.logistic_code,
                            shipper_name: latestExpressInfo.shipper_name,
                        };
                        returnObj.sendExpress = express;
                        sendExpress.push(express);
                    } else if (item.express_type == 2) {
                        const latestExpressInfo = await this.model('order_express').queryExpress100(orderId, item.sender_phone, 2);
                        const expressObj = JSON.parse(latestExpressInfo.traces);

                        const express = {
                            request_time: moment.unix(latestExpressInfo.request_time).format('YYYY-MM-DD HH:mm:ss'),
                            is_finish: latestExpressInfo.is_finish,
                            traces: expressObj.data || JSON.parse(latestExpressInfo.traces),
                            logistic_code: latestExpressInfo.logistic_code,
                            shipper_name: latestExpressInfo.shipper_name,
                        };
                        returnObj.returnExpress = express;
                        returnExpress.push(express);
                    }
                }

                returnObj.sendExpressList = sendExpress;
                returnObj.returnExpressList = returnExpress;
            }
        }

        return this.success(returnObj);
    }
    /**
     * 查询订单状态
     */
    async orderStatusAction() {
        const userId = this.getLoginUserId();
        const orderId = this.post('orderId');
        const orderSn = this.post('orderSn');
        if (think.isEmpty(orderId) && think.isEmpty(orderSn)) {
            return this.fail('订单号不能为空');
        }
        // 使用 orderId 或者 orderSn 查询订单
        let where = {
            user_id: userId,
        };
        if (!think.isEmpty(orderId)) {
            where.id = orderId;
        }
        if (!think.isEmpty(orderSn)) {
            where.order_sn = orderSn;
        }
        const orderInfo = await this.model('order')
            .where(where).find();
        return this.success({
            orderStatus: orderInfo.order_status
        });
    }
    /**
     * order 和 order-check 的goodslist
     * @return {Promise} []
     */
    async orderGoodsAction() {
        const userId = this.getLoginUserId();;
        const orderId = this.get('orderId');
        if (orderId > 0) {
            const orderGoods = await this.model('order_goods').where({
                user_id: userId,
                order_id: orderId,
                is_delete: 0
            }).select();
            var goodsCount = 0;
            for (const gitem of orderGoods) {
                goodsCount += gitem.number;
            }
            return this.success(orderGoods);
        } else {
            const cartList = await this.model('cart').where({
                user_id: userId,
                checked: 1,
                is_delete: 0,
                is_fast: 0,
            }).select();
            return this.success(cartList);
        }
    }
    /**
     * 取消订单
     * @return {Promise} []
     */
    async cancelAction() {
        const orderId = this.post('orderId');
        const userId = this.getLoginUserId();

        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderId);
        if (!handleOption.cancel) {
            return this.fail('订单不能取消');
        }

        // 获取订单信息，检查是否使用了优惠券
        const orderInfo = await this.model('order').where({
            id: orderId,
            user_id: userId
        }).find();

        if (think.isEmpty(orderInfo)) {
            return this.fail('订单不存在');
        }

        // 设置订单已取消状态
        let updateInfo = {
            order_status: 102,
            cancel_time: parseInt(new Date().getTime() / 1000),
        };

        try {
            await this.transaction('order', async (session) => {
                // 1. 取消订单，还原库存
                const goodsInfo = await this.model('order_goods').db(session).where({
                    order_id: orderId,
                    user_id: userId
                }).select();

                for (const item of goodsInfo) {
                    let goods_id = item.goods_id;
                    let product_id = item.product_id;
                    let number = item.number;
                    await this.model('goods').db(session).where({
                        id: goods_id
                    }).increment('goods_number', number);
                    await this.model('product').db(session).where({
                        id: product_id
                    }).increment('goods_number', number);
                }

                // 2. 更新订单状态
                await this.model('order').db(session).where({
                    id: orderId
                }).update(updateInfo);

                // 3. 如果使用了优惠券，需要退还优惠券（仅限取消订单，不包括退款订单）
                if (orderInfo.coupon_code && orderInfo.coupon_template_id > 0) {
                    const couponService = this.service('coupon');
                    const refundResult = await couponService.refundCoupon(orderInfo.coupon_code, userId);
                    if (!refundResult) {
                        think.logger.error(`订单取消时退还优惠券失败，订单ID: ${orderId}, 优惠券码: ${orderInfo.coupon_code}`);
                        // 注意：这里不抛出异常，避免因为优惠券退还失败导致整个取消订单失败
                        // 可以考虑记录到专门的错误日志中，后续人工处理
                    } else {
                        think.logger.info(`订单取消成功退还优惠券，订单ID: ${orderId}, 优惠券码: ${orderInfo.coupon_code}`);
                    }
                }
            });

            return this.success('订单取消成功');
        } catch (error) {
            think.logger.error('取消订单失败:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            return this.fail('订单取消失败');
        }
    }
    /**
     * 订单申请退款
     * @return {Promise} []
     */
    async applyRefundAction() {
        const orderId = this.post('orderId');
        // 检测是否能够取消
        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        const handleOption = await this.model('order').getOrderHandleOption(orderId, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能申请退款');
        }
        const succesInfo = await this.model('order').where({
            id: orderId
        }).update({
            order_status: 202,
            apply_status: orderInfo.order_status
        });

        // 发送退款申请推送通知给管理员
        try {
            const pushService = think.service('push', 'common');
            await pushService.sendOrderRefundApplyNotification(orderInfo);
        } catch (pushError) {
            think.logger.error('发送订单退款申请推送通知失败:', pushError);
        }

        return this.success(succesInfo);
    }
    /**
     * 取消申请退款
     */
    async refundCancelAction() {
        const orderId = this.post('orderId');
        // 检测是否能够取消
        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        const handleOption = await this.model('order').getOrderHandleOption(orderId, orderInfo);
        if (!handleOption.refund_cancel) {
            return this.fail('订单不能取消退款');
        }
        const succesInfo = await this.model('order').where({
            id: orderId
        }).update({
            order_status: orderInfo.apply_status || 401,
        });
        return this.success(succesInfo);
    }

    async addRefundOrderExpressAction() {
        const orderId = this.post('orderId');
        const logistic_code = this.post('logisticCode');
        const sender_phone = this.post('senderPhone');
        const sender_name = this.post('senderName');

        if (think.isEmpty(logistic_code) || think.isEmpty(orderId)
            || think.isEmpty(sender_phone) || think.isEmpty(sender_name)) {
            return this.fail('参数错误');
        }

        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        if (think.isEmpty(orderInfo)) {
            return this.fail('订单信息错误');
        }
        if (!(orderInfo.order_status == 205)) {
            return this.fail('订单状态错误');
        }

        // 调用快递100的接口，识别物流号信息
        const identifyExpressNum = await this.model('order_express').identifyExpressNum(logistic_code);
        think.logger.info("识别快递单号快递100响应：", identifyExpressNum);

        if (think.isEmpty(identifyExpressNum) || !(identifyExpressNum instanceof Array)) {
            return this.fail(identifyExpressNum.message || '无效快递单号');
        }
        const expressInfo = await this.model('order_express').where({
            order_id: orderId,
            express_type: 2,
            is_delete: 0
        }).find();
        if (!think.isEmpty(expressInfo)) {
            await this.model('order_express').where({
                order_id: orderId
            }).update({
                logistic_code: logistic_code,
                shipper_name: identifyExpressNum[0].name,
                shipper_code: identifyExpressNum[0].comCode,
                sender_phone: sender_phone,
                traces: '',
                sender_name: sender_name
            });
        } else {
            await this.model('order_express').add({
                logistic_code: logistic_code,
                express_type: 2,
                order_id: orderId,
                shipper_name: identifyExpressNum[0].name,
                shipper_code: identifyExpressNum[0].comCode,
                sender_phone: sender_phone,
                traces: '',
                sender_name: sender_name
            });
        }

        // 发送退货运单号提交推送通知给管理员
        try {
            const expressInfo = {
                logistic_code: logistic_code,
                shipper_name: identifyExpressNum[0].name,
                shipper_code: identifyExpressNum[0].comCode,
                sender_phone: sender_phone,
                sender_name: sender_name
            };
            const pushService = think.service('push', 'common');
            await pushService.sendRefundExpressNotification(orderInfo, expressInfo);
        } catch (pushError) {
            think.logger.error('发送退货运单号提交推送通知失败:', pushError);
        }

        return this.success();
    }
    /**
     * 删除订单
     * @return {Promise} []
     */
    async deleteAction() {
        const orderId = this.post('orderId');
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderId);
        if (!handleOption.delete) {
            return this.fail('订单不能删除');
        }
        const succesInfo = await this.model('order').orderDeleteById(orderId);
        return this.success(succesInfo);
    }
    /**
     * 确认订单
     * @return {Promise} []
     */
    async confirmAction() {
        const orderId = this.post('orderId');
        const actType = this.post('actType');
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderId);
        if (!handleOption.confirm) {
            return this.fail('订单不能确认');
        }
        if (actType == 'auto') { // 自动确认收货
            // 检查确认订单的时间是否已经到了
            const orderInfo = await this.model('order').where({
                id: orderId
            }).find();
            // 状态只有 已发货，已收货才能自动确认
            if ([301, 302, 303].indexOf(parseInt(orderInfo.order_status)) == -1) {
                return this.fail('状态只有 已发货，已收货才能自动确认');
            }
            // 发货时间不足7天
            if (parseInt(new Date().getTime() / 1000) < orderInfo.shipping_time + 7 * 24 * 60 * 60) {
                return this.fail('发货时间不足7天');
            }
        }
        // 设置订单已确认状态
        const currentTime = parseInt(new Date().getTime() / 1000);
        let updateInfo = {
            order_status: 401,
            confirm_time: currentTime
        };
        const succesInfo = await this.model('order').where({
            id: orderId
        }).update(updateInfo);
        return this.success(succesInfo);
    }
    /**
     * 完成评论后的订单
     * @return {Promise} []
     */
    async completeAction() {
        const orderId = this.post('orderId');
        // 设置订单已完成
        const currentTime = parseInt(new Date().getTime() / 1000);
        let updateInfo = {
            order_status: 401,
            dealdone_time: currentTime
        };
        const succesInfo = await this.model('order').where({
            id: orderId
        }).update(updateInfo);
        return this.success(succesInfo);
    }
    /**
     * 提交订单
     * !!! 已作废
     * @returns {Promise.<void>}
     */
    async submitAction() {
        // 获取收货地址信息和计算运费
        const userId = this.getLoginUserId();
        const addressId = this.post('addressId');
        const freightPrice = this.post('freightPrice');
        const offlinePay = this.post('offlinePay');
        let postscript = this.post('postscript');
        const checkedAddress = await this.model('address').where({
            id: addressId
        }).find();
        if (think.isEmpty(checkedAddress)) {
            return this.fail('请选择收货地址');
        }
        // 获取要购买的商品
        const checkedGoodsList = await this.model('cart').where({
            user_id: userId,
            checked: 1,
            is_delete: 0
        }).select();
        if (think.isEmpty(checkedGoodsList)) {
            return this.fail('请选择商品');
        }
        let checkPrice = 0;
        let checkStock = 0;
        for (const item of checkedGoodsList) {
            let product = await this.model('product').where({
                id: item.product_id
            }).find();
            if (item.number > product.goods_number) {
                checkStock++;
            }
            if (item.retail_price != item.add_price) {
                checkPrice++;
            }
        }
        if (checkStock > 0) {
            return this.fail(400, '库存不足，请重新下单');
        }
        if (checkPrice > 0) {
            return this.fail(400, '价格发生变化，请重新下单');
        }
        // 获取订单使用的红包
        // 如果有用红包，则将红包的数量减少，当减到0时，将该条红包删除
        // 统计商品总价
        let goodsTotalPrice = 0.00;
        for (const cartItem of checkedGoodsList) {
            goodsTotalPrice += cartItem.number * cartItem.retail_price;
        }
        // 订单价格计算
        const orderTotalPrice = goodsTotalPrice + freightPrice; // 订单的总价
        const actualPrice = orderTotalPrice - 0.00; // 减去其它支付的金额后，要实际支付的金额 比如满减等优惠
        const currentTime = parseInt(new Date().getTime() / 1000);
        let print_info = '';
        for (const item in checkedGoodsList) {
            let i = Number(item) + 1;
            print_info = print_info + i + '、' + checkedGoodsList[item].goods_aka + '【' + checkedGoodsList[item].number + '】 ';
        }

        // const checkedAddress = await this.model('address').where({id: addressId}).find();
        const orderInfo = {
            order_sn: this.model('order').generateOrderNumber(),
            user_id: userId,
            // 收货地址和运费
            consignee: checkedAddress.name,
            mobile: checkedAddress.mobile,
            province: checkedAddress.province_id,
            city: checkedAddress.city_id,
            district: checkedAddress.district_id,
            address: checkedAddress.address,
            order_status: 101, // 订单初始状态为 101
            // 根据城市得到运费，这里需要建立表：所在城市的具体运费
            freight_price: freightPrice,
            postscript: postscript,
            add_time: currentTime,
            goods_price: goodsTotalPrice,
            order_price: orderTotalPrice,
            actual_price: actualPrice,
            change_price: actualPrice,
            print_info: print_info,
            offline_pay: offlinePay
        };
        // 开启事务，插入订单信息和订单商品
        const orderId = await this.model('order').add(orderInfo);
        orderInfo.id = orderId;
        if (!orderId) {
            return this.fail('订单提交失败');
        }
        // 将商品信息录入数据库
        const orderGoodsData = [];
        for (const goodsItem of checkedGoodsList) {
            orderGoodsData.push({
                user_id: userId,
                order_id: orderId,
                goods_id: goodsItem.goods_id,
                product_id: goodsItem.product_id,
                goods_name: goodsItem.goods_name,
                goods_aka: goodsItem.goods_aka,
                list_pic_url: goodsItem.list_pic_url,
                retail_price: goodsItem.retail_price,
                number: goodsItem.number,
                goods_specifition_name: goodsItem.goods_specifition_name,
                freight_template_id: 0, // 旧版本订单使用默认运费模板
                freight_name: '默认物流', // 设置默认运费名称
                freight_price: freightPrice // 设置运费价格
            });
        }
        await this.model('order_goods').addMany(orderGoodsData);
        await this.model('cart').clearBuyGoods();
        return this.success({
            orderInfo: orderInfo
        });
    }

    /**
     * 创建订单接口
     * @returns {Promise.<void>}
     */
    async createOrderAction() {
        const userId = this.getLoginUserId();
        const userAgent = this.getUserAgent();
        const orderId = this.post('orderId'); // 新增：订单ID，用于区分创建还是更新
        const addressId = this.post('addressId');
        const offlinePay = this.post('offlinePay');
        const postscript = this.post('postscript') || '';
        const remark = this.post('remark') || '';
        const goodsList = this.post('goodsList');// 0：购物车下单，1:立即购买，2:再来一单
        const couponCode = this.post('couponCode') || ''; // 新增：优惠券码
        const selectedFreightTemplateId = this.post('freightTemplateId') || null; // 新增：选择的运费模板ID

        const addType = this.post('addType');
        const orderType = this.post('orderType');

        // 判断是创建订单还是更新订单
        if (orderId) {
            return await this.updateOrderAction(orderId, {
                addressId,
                offlinePay,
                postscript,
                remark,
                goodsList,
                couponCode,
                selectedFreightTemplateId,
                addType,
                orderType,
                userId,
                userAgent
            });
        } else {
            return await this.createNewOrderAction({
                addressId,
                offlinePay,
                postscript,
                remark,
                goodsList,
                couponCode,
                selectedFreightTemplateId,
                addType,
                orderType,
                userId,
                userAgent
            });
        }
    }

    /**
     * 创建新订单
     */
    async createNewOrderAction(params) {
        const {
            addressId, offlinePay, postscript, remark, goodsList,
            couponCode, selectedFreightTemplateId, addType, orderType,
            userId, userAgent
        } = params;

        const goodsModel = this.model('goods');
        const addressModel = this.model('address');

        // 校验商品、库存、价格、地址及运费
        const checkGoods = await this.service('goods')
            .checkGoodsOrder(goodsList, addressId, userId, userAgent, goodsModel, addressModel, selectedFreightTemplateId);

        const goodsTotalPrice = parseFloat(checkGoods.goodsTotalPrice); // 商品总价
        const freightPrice = parseFloat(checkGoods.freightPrice); // 运费
        let actualPrice = goodsTotalPrice + freightPrice; // 实际支付金额

        const checkedGoodsList = checkGoods.checkedGoodsList; // 校验后的商品列表
        const checkedAddress = checkGoods.checkedAddress; // 校验后的地址

        // 优惠券处理
        let couponDiscount = 0;
        let couponInfo = null;

        if (couponCode) {
            const couponService = this.service('coupon');
            const validationResult = await couponService.validateCoupon(couponCode, userId, actualPrice);

            if (!validationResult.valid) {
                return this.fail(validationResult.message);
            }

            couponDiscount = validationResult.discountAmount;
            couponInfo = validationResult.coupon;
            actualPrice = Math.max(0, actualPrice - couponDiscount); // 确保金额不为负数
        }

        const currentTime = parseInt(Date.now() / 1000);
        const orderModel = this.model('order');
        const orderGoodsModel = this.model('order_goods');
        const cartModel = this.model('cart');

        // 构建打印信息
        let print_info = '';
        for (let i = 0; i < checkedGoodsList.length; i++) {
            const item = checkedGoodsList[i];
            let num = goodsList.filter(goods => goods.goodsId == item.goods_id && goods.productId == item.product_id);
            if (num.length > 0 && num[0].number) {
                print_info += `${i + 1}、${item.product_name}【型号：${item.goods_specifition_name}】【数量：${num[0].number}】 `;
            } else {
                print_info += `${i + 1}、${item.product_name}【型号：${item.goods_specifition_name}】 `;
            }
        }

        const orderInfo = {
            order_sn: orderModel.generateOrderNumber(),
            user_id: userId,
            consignee: checkedAddress.name,
            mobile: checkedAddress.mobile,
            province: checkedAddress.province_id,
            city: checkedAddress.city_id,
            district: checkedAddress.district_id,
            address: checkedAddress.address,
            order_status: 101,
            freight_template_id: selectedFreightTemplateId || 0,
            freight_price: freightPrice,
            postscript: postscript,
            add_time: currentTime,
            goods_price: goodsTotalPrice,
            order_price: goodsTotalPrice + freightPrice, // 订单原价
            actual_price: actualPrice, // 实际支付金额
            change_price: actualPrice,
            print_info: print_info,
            offline_pay: offlinePay,
            order_type: orderType,
            address_id: addressId,
            remark: remark,
            coupon_code: couponCode, // 新增：记录使用的优惠券码
            coupon_discount: couponDiscount, // 新增：记录优惠券折扣金额
            coupon_template_id: think.isEmpty(couponInfo) ? 0 : couponInfo.coupon_template_id // 新增：记录优惠券ID
        };

        const insertIdRes = await this.transaction("order", async (session) => {
            // 插入订单
            const orderId = await orderModel.db(session).add(orderInfo);
            if (!orderId) throw new Error('订单创建失败');

            // 插入订单商品
            const orderGoodsData = checkedGoodsList.map(item => {
                return {
                    user_id: userId,
                    order_id: orderId,
                    goods_id: item.goods_id,
                    product_id: item.product_id,
                    goods_name: item.goods_name,
                    goods_aka: item.product_name,
                    goods_sn: item.goods_sn,
                    list_pic_url: item.list_pic_url,
                    retail_price: userAgent ? item.agent_price : item.retail_price,
                    number: item.buy_number,
                    goods_specifition_name: item.goods_specifition_name,
                    freight_template_id: item.freight_template_id || 0, // 使用商品的运费模板ID
                    freight_name: item.freight_template_name || '默认物流', // 使用运费模板名称
                    freight_price: freightPrice // 设置运费价格
                }
            });
            await orderGoodsModel.db(session).addMany(orderGoodsData);

            // 使用优惠券
            if (couponCode) {
                const couponService = this.service('coupon');
                const useResult = await couponService.useCoupon(couponCode, userId, orderId);
                if (!useResult) {
                    throw new Error('优惠券使用失败');
                }
            }

            if (addType == 0) {
                // 提取当前下单商品的 product_id 集合
                const productIds = checkedGoodsList.map(item => item.product_id);

                // 清除购物车中本次已下单的商品
                await cartModel.db(session)
                    .where({ user_id: userId, product_id: ['IN', productIds] })
                    .update({ is_delete: 1 });
            }

            return orderId;
        });

        // 返回创建好的订单信息
        return this.success({
            id: insertIdRes,
            order_sn: orderInfo.order_sn,
            actual_price: actualPrice,
            freight_price: freightPrice,
            goods_price: goodsTotalPrice,
            coupon_discount: couponDiscount,
            coupon_info: couponInfo ? {
                name: couponInfo.template_name,
                type: couponInfo.type,
                discount: couponInfo.discount
            } : null
        });
    }

    /**
     * 更新订单信息
     */
    async updateOrderAction(orderId, params) {
        const {
            addressId, offlinePay, postscript, remark, goodsList,
            couponCode, selectedFreightTemplateId, addType, orderType,
            userId, userAgent
        } = params;

        // 查询现有订单
        const orderModel = this.model('order');
        const existingOrder = await orderModel.where({
            id: orderId,
            user_id: userId,
            is_delete: 0
        }).find();

        if (think.isEmpty(existingOrder)) {
            return this.fail('订单不存在或无权限访问');
        }

        // 检查订单状态是否允许更新
        if (existingOrder.order_status !== 101) {
            return this.fail('订单状态不允许修改');
        }

        const goodsModel = this.model('goods');
        const addressModel = this.model('address');

        // 校验商品、库存、价格、地址及运费
        const checkGoods = await this.service('goods')
            .checkGoodsOrder(goodsList, addressId, userId, userAgent, goodsModel, addressModel, selectedFreightTemplateId);

        const goodsTotalPrice = parseFloat(checkGoods.goodsTotalPrice); // 商品总价
        const freightPrice = parseFloat(checkGoods.freightPrice); // 运费
        let actualPrice = goodsTotalPrice + freightPrice; // 实际支付金额

        const checkedGoodsList = checkGoods.checkedGoodsList; // 校验后的商品列表
        const checkedAddress = checkGoods.checkedAddress; // 校验后的地址

        // 优惠券处理
        let couponDiscount = 0;
        let couponInfo = null;

        if (couponCode) {
            const couponService = this.service('coupon');
            const validationResult = await couponService.validateCoupon(couponCode, userId, actualPrice);

            if (!validationResult.valid) {
                return this.fail(validationResult.message);
            }

            couponDiscount = validationResult.discountAmount;
            couponInfo = validationResult.coupon;
            actualPrice = Math.max(0, actualPrice - couponDiscount); // 确保金额不为负数
        }

        const currentTime = parseInt(Date.now() / 1000);
        const orderGoodsModel = this.model('order_goods');

        // 构建打印信息
        let print_info = '';
        for (let i = 0; i < checkedGoodsList.length; i++) {
            const item = checkedGoodsList[i];
            let num = goodsList.filter(goods => goods.goodsId == item.goods_id && goods.productId == item.product_id);
            if (num.length > 0 && num[0].number) {
                print_info += `${i + 1}、${item.goods_name}【型号：${item.goods_specifition_name}】【数量：${num[0].number}】 `;
            } else {
                print_info += `${i + 1}、${item.goods_name}【型号：${item.goods_specifition_name}】 `;
            }
        }

        // 更新订单信息
        const updateOrderInfo = {
            consignee: checkedAddress.name,
            mobile: checkedAddress.mobile,
            province: checkedAddress.province_id,
            city: checkedAddress.city_id,
            district: checkedAddress.district_id,
            address: checkedAddress.address,
            // TODO: 需要处理此处的 freight_template_id
            freight_template_id: selectedFreightTemplateId || 0,
            freight_price: freightPrice,
            postscript: postscript,
            goods_price: goodsTotalPrice,
            order_price: goodsTotalPrice + freightPrice, // 订单原价
            actual_price: actualPrice, // 实际支付金额
            change_price: actualPrice,
            print_info: print_info,
            offline_pay: offlinePay,
            order_type: orderType,
            address_id: addressId,
            remark: remark,
            coupon_code: couponCode,
            coupon_discount: couponDiscount,
            coupon_template_id: think.isEmpty(couponInfo) ? 0 : couponInfo.coupon_template_id,
        };

        await this.transaction("order", async (session) => {
            // 更新订单基本信息
            await orderModel.db(session).where({ id: orderId }).update(updateOrderInfo);

            // 删除原有订单商品
            await orderGoodsModel.db(session).where({ order_id: orderId }).update({ is_delete: 1 });

            // 插入新的订单商品
            const orderGoodsData = checkedGoodsList.map(item => {
                return {
                    user_id: userId,
                    order_id: orderId,
                    goods_id: item.goods_id,
                    product_id: item.product_id,
                    goods_name: item.goods_name,
                    goods_aka: item.product_name,
                    goods_sn: item.goods_sn,
                    list_pic_url: item.list_pic_url,
                    retail_price: userAgent ? item.agent_price : item.retail_price,
                    number: item.buy_number,
                    goods_specifition_name: item.goods_specifition_name,
                }
            });
            await orderGoodsModel.db(session).addMany(orderGoodsData);

            // 处理优惠券变更
            if (existingOrder.coupon_code && existingOrder.coupon_code !== couponCode) {
                // 如果原来有优惠券且现在更换了，需要释放原优惠券
                const couponService = this.service('coupon');
                await couponService.refundCoupon(existingOrder.coupon_code, userId);
            }

            // 使用新的优惠券
            if (couponCode && couponCode !== existingOrder.coupon_code) {
                const couponService = this.service('coupon');
                const useResult = await couponService.useCoupon(couponCode, userId, orderId);
                if (!useResult) {
                    throw new Error('优惠券使用失败');
                }
            }
        });

        // 返回更新后的订单信息
        return this.success({
            id: orderId,
            order_sn: existingOrder.order_sn,
            actual_price: actualPrice,
            freight_price: freightPrice,
            goods_price: goodsTotalPrice,
            coupon_discount: couponDiscount,
            coupon_info: couponInfo ? {
                name: couponInfo.template_name,
                type: couponInfo.type,
                discount: couponInfo.discount
            } : null,
            updated: true
        });
    }


    async updateAction() {
        const addressId = this.post('addressId');
        const orderId = this.post('orderId');
        // 备注
        // let postscript = this.post('postscript');
        // const buffer = Buffer.from(postscript);
        const updateAddress = await this.model('address').where({
            id: addressId
        }).find();
        const currentTime = parseInt(new Date().getTime() / 1000);
        const orderInfo = {
            // 收货地址和运费
            consignee: updateAddress.name,
            mobile: updateAddress.mobile,
            province: updateAddress.province_id,
            city: updateAddress.city_id,
            district: updateAddress.district_id,
            address: updateAddress.address,
            // TODO 根据地址计算运费
            // freight_price: 0.00,
            // add_time: currentTime
        };
        const updateInfo = await this.model('order').where({
            id: orderId
        }).update(orderInfo);
        return this.success(updateInfo);
    }

    /************* 新物流查询 ********** */
    async getOrderExpressAction() {
        const orderId = this.get('orderId');
        const mobile = this.get('mobile');
        const latestExpressInfo = await this.model('order_express').queryExpress100(orderId, mobile, 1);
        const expressObj = JSON.parse(latestExpressInfo.traces);

        return this.success({
            request_time: moment.unix(latestExpressInfo.request_time).format('YYYY-MM-DD HH:mm:ss'),
            is_finish: latestExpressInfo.is_finish,
            traces: expressObj.data || JSON.parse(latestExpressInfo.traces),
            logistic_code: latestExpressInfo.logistic_code,
            shipper_name: latestExpressInfo.shipper_name,
        });
    }
    // 查询退款订单物流信息
    async getRefundExpressAction() {
        const orderId = this.get('orderId');
        if (think.isEmpty(orderId)) {
            return this.fail('参数错误');
        }
        const expressInfo = await this.model('order_express').where({
            order_id: orderId,
            express_type: 2,
            is_delete: 0
        }).find();
        if (think.isEmpty(expressInfo)) {
            return this.success({
                traces: [],
                logistic_code: "",
            });
        }
        const latestExpressInfo = await this.model('order_express').queryExpress100(orderId, expressInfo.sender_phone, 2, expressInfo);
        const expressObj = JSON.parse(latestExpressInfo.traces);

        return this.success({
            request_time: moment.unix(latestExpressInfo.request_time).format('YYYY-MM-DD HH:mm:ss'),
            is_finish: latestExpressInfo.is_finish,
            traces: expressObj.data || JSON.parse(latestExpressInfo.traces),
            logistic_code: latestExpressInfo.logistic_code,
            shipper_name: latestExpressInfo.shipper_name,
        });
    }

    /**
     * 获取订单可用的优惠券列表
     */
    async getAvailableCouponsAction() {
        const userId = this.getLoginUserId();
        const orderAmount = parseFloat(this.post('orderAmount')) || 0;

        if (orderAmount <= 0) {
            return this.fail('订单金额必须大于0');
        }

        const couponService = this.service('coupon');
        const availableCoupons = await couponService.getAvailableCouponsForOrder(userId, orderAmount);

        return this.success(availableCoupons);
    }

    /**
     * 验证优惠券
     */
    async validateCouponAction() {
        const userId = this.getLoginUserId();
        const couponCode = this.post('couponCode');
        const orderAmount = parseFloat(this.post('orderAmount')) || 0;

        if (!couponCode) {
            return this.fail('优惠券码不能为空');
        }

        if (orderAmount <= 0) {
            return this.fail('订单金额必须大于0');
        }

        const couponService = this.service('coupon');
        const validationResult = await couponService.validateCoupon(couponCode, userId, orderAmount);

        if (!validationResult.valid) {
            return this.fail(validationResult.message);
        }

        return this.success({
            coupon: validationResult.coupon,
            discountAmount: validationResult.discountAmount,
            finalAmount: parseFloat(orderAmount - validationResult.discountAmount).toFixed(2)
        });
    }

    /**
     * 查询当前订单所有的物流单号
     */
    async getOrderExpressListAction() {
        const orderId = this.get('orderId');
        if (think.isEmpty(orderId)) {
            return this.fail('参数错误');
        }
        const expressList = await this.model('order_express').where({
            order_id: orderId,
            is_delete: 0
        }).select();
        return this.success(expressList);
    }

    /**
     * 创建订单接口（支持运费模板分组）
     * 支持每组商品使用不同的运费模板，并在 order_goods 表中记录模板ID和运费金额
     */
    async createOrderWithFreightGroupsAction() {
        const userId = this.getLoginUserId();
        const userAgent = this.getUserAgent();
        const orderId = this.post('orderId'); // 订单ID，用于区分创建还是更新
        const addressId = this.post('addressId');
        const offlinePay = this.post('offlinePay');
        const postscript = this.post('postscript') || '';
        const remark = this.post('remark') || '';
        const couponCode = this.post('couponCode') || '';
        const addType = this.post('addType'); // 0：购物车下单，1:立即购买，2:再来一单
        const orderType = this.post('orderType');
        const templateGroups = this.post('templateGroups'); // 运费模板分组数据

        // 判断是创建订单还是更新订单
        if (orderId) {
            return await this.updateOrderWithFreightGroupsAction(orderId, {
                addressId,
                offlinePay,
                postscript,
                remark,
                couponCode,
                addType,
                orderType,
                templateGroups,
                userId,
                userAgent
            });
        } else {
            return await this.createNewOrderWithFreightGroupsAction({
                addressId,
                offlinePay,
                postscript,
                remark,
                couponCode,
                addType,
                orderType,
                templateGroups,
                userId,
                userAgent
            });
        }
    }

    /**
     * 创建新订单（支持运费模板分组）
     */
    async createNewOrderWithFreightGroupsAction(params) {
        const {
            addressId, offlinePay, postscript, remark,
            couponCode, addType, orderType, templateGroups,
            userId, userAgent
        } = params;

        if (!addressId) {
            return this.fail('收货地址不能为空');
        }

        if (think.isEmpty(templateGroups)) {
            return this.fail('商品分组信息不能为空');
        }

        try {
            const goodsModel = this.model('goods');
            const addressModel = this.model('address');
            const orderModel = this.model('order');
            const orderGoodsModel = this.model('order_goods');
            const cartModel = this.model('cart');

            // 获取地址信息
            const address = await addressModel.where({ id: addressId, user_id: userId, is_delete: 0 }).find();
            if (think.isEmpty(address)) {
                return this.fail('收货地址有误');
            }

            // 构建商品列表用于校验
            let allGoodsList = [];
            let totalFreightPrice = 0;
            let allOrderGoodsData = [];

            // 处理每个运费模板分组
            for (const group of templateGroups) {
                const { template_info, goods_list } = group;
                const selectedTemplate = template_info; // template_info现在是一个对象，不再是数组

                if (!selectedTemplate) {
                    return this.fail('请为每个商品分组选择运费模板');
                }

                // 累加运费
                totalFreightPrice += parseFloat(selectedTemplate.freight_price);

                // 处理该分组的商品
                for (const goods of goods_list) {
                    allGoodsList.push({
                        goodsId: goods.goods_id,
                        productId: goods.product_id,
                        number: goods.buy_number
                    });

                    // 准备订单商品数据
                    allOrderGoodsData.push({
                        user_id: userId,
                        goods_id: goods.goods_id,
                        product_id: goods.product_id,
                        goods_name: goods.goods_name,
                        goods_aka: goods.goods_aka,
                        goods_sn: goods.goods_sn || '',
                        list_pic_url: goods.list_pic_url,
                        retail_price: goods.retail_price,
                        number: goods.buy_number,
                        goods_specifition_name: goods.goods_specifition_name || '',
                        freight_template_id: selectedTemplate.template_id, // 记录该商品使用的运费模板ID
                        freight_name: selectedTemplate.template_name,
                        freight_price: selectedTemplate.freight_price // 记录该组商品使用的完整运费价格
                    });
                }
            }

            // 校验商品库存和价格
            const { checkPudList, goodsTotalPrice } = await this.service('goods')
                ._checkGoodsAndCalcTotal(allGoodsList, goodsModel, userAgent);

            let actualPrice = goodsTotalPrice + totalFreightPrice; // 实际支付金额

            // 优惠券处理
            let couponDiscount = 0;
            let couponInfo = null;

            if (couponCode) {
                const couponService = this.service('coupon');
                const validationResult = await couponService.validateCoupon(couponCode, userId, actualPrice);

                if (!validationResult.valid) {
                    return this.fail(validationResult.message);
                }

                couponDiscount = validationResult.discountAmount;
                couponInfo = validationResult.coupon;
                actualPrice = Math.max(0, actualPrice - couponDiscount);
            }

            const currentTime = parseInt(Date.now() / 1000);

            // 构建打印信息
            let print_info = '';
            for (let i = 0; i < checkPudList.length; i++) {
                const item = checkPudList[i];
                print_info += `${i + 1}、${item.product_name || item.goods_name}【型号：${item.goods_specifition_name}】【数量：${item.buy_number}】 `;
            }

            const orderInfo = {
                order_sn: orderModel.generateOrderNumber(),
                user_id: userId,
                consignee: address.name,
                mobile: address.mobile,
                province: address.province_id,
                city: address.city_id,
                district: address.district_id,
                address: address.address,
                order_status: 101,
                freight_price: totalFreightPrice,
                postscript: postscript,
                add_time: currentTime,
                goods_price: goodsTotalPrice,
                order_price: goodsTotalPrice + totalFreightPrice,
                actual_price: actualPrice,
                change_price: actualPrice,
                print_info: print_info,
                offline_pay: offlinePay,
                order_type: orderType,
                address_id: addressId,
                remark: remark,
                coupon_code: couponCode,
                coupon_discount: couponDiscount,
                coupon_template_id: think.isEmpty(couponInfo) ? 0 : couponInfo.coupon_template_id
            };

            const insertIdRes = await this.transaction("order", async (session) => {
                // 插入订单
                const orderId = await orderModel.db(session).add(orderInfo);
                if (!orderId) throw new Error('订单创建失败');

                // 为每个订单商品设置订单ID
                allOrderGoodsData.forEach(item => {
                    item.order_id = orderId;
                });

                // 插入订单商品
                await orderGoodsModel.db(session).addMany(allOrderGoodsData);

                // 使用优惠券
                // if (couponCode) {
                //     const couponService = this.service('coupon');
                //     const useResult = await couponService.useCoupon(couponCode, userId, orderId);
                //     if (!useResult) {
                //         throw new Error('优惠券使用失败');
                //     }
                // }

                if (addType == 0) {
                    // 提取当前下单商品的 product_id 集合
                    const productIds = allOrderGoodsData.map(item => item.product_id);

                    // 清除购物车中本次已下单的商品
                    await cartModel.db(session)
                        .where({ user_id: userId, product_id: ['IN', productIds] })
                        .update({ is_delete: 1 });
                }

                return orderId;
            });

            // 返回创建好的订单信息
            return this.success({
                id: insertIdRes,
                order_sn: orderInfo.order_sn,
                actual_price: actualPrice,
                freight_price: totalFreightPrice,
                goods_price: goodsTotalPrice,
                coupon_discount: couponDiscount,
                coupon_info: couponInfo ? {
                    name: couponInfo.template_name,
                    type: couponInfo.type,
                    discount: couponInfo.discount
                } : null
            });

        } catch (error) {
            think.logger.error('创建订单失败:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            return this.fail(error.message || '订单创建失败');
        }
    }

    /**
     * 更新订单信息（支持运费模板分组）
     */
    async updateOrderWithFreightGroupsAction(orderId, params) {
        const {
            addressId, offlinePay, postscript, remark,
            couponCode, addType, orderType, templateGroups,
            userId, userAgent
        } = params;

        try {
            // 查询现有订单
            const orderModel = this.model('order');
            const existingOrder = await orderModel.where({
                id: orderId,
                user_id: userId,
                is_delete: 0
            }).find();

            if (think.isEmpty(existingOrder)) {
                return this.fail('订单不存在或无权限访问');
            }

            // 检查订单状态是否允许更新
            if (existingOrder.order_status !== 101) {
                return this.fail('订单状态不允许修改');
            }

            const addressModel = this.model('address');
            const orderGoodsModel = this.model('order_goods');

            // 获取地址信息
            const address = await addressModel.where({ id: addressId, user_id: userId, is_delete: 0 }).find();
            if (think.isEmpty(address)) {
                return this.fail('收货地址有误');
            }

            // 构建商品列表
            let allGoodsList = [];
            let totalFreightPrice = 0;
            let allOrderGoodsData = [];

            // 处理每个运费模板分组
            for (const group of templateGroups) {
                const { template_info, goods_list } = group;
                const selectedTemplate = template_info; // template_info现在是一个对象，不再是数组

                if (!selectedTemplate) {
                    return this.fail('请为每个商品分组选择运费模板');
                }

                // 累加运费
                totalFreightPrice += parseFloat(selectedTemplate.freight_price);

                // 处理该分组的商品
                for (const goods of goods_list) {
                    allGoodsList.push({
                        goodsId: goods.goods_id,
                        productId: goods.product_id,
                        number: goods.buy_number
                    });

                    // 准备订单商品数据
                    allOrderGoodsData.push({
                        user_id: userId,
                        order_id: orderId,
                        goods_id: goods.goods_id,
                        product_id: goods.product_id,
                        goods_name: goods.goods_name,
                        goods_aka: goods.goods_name,
                        goods_sn: goods.goods_sn || '',
                        list_pic_url: goods.list_pic_url,
                        retail_price: goods.retail_price,
                        number: goods.buy_number,
                        goods_specifition_name: goods.goods_specifition_name || '',
                        freight_template_id: selectedTemplate.template_id, // 记录该商品使用的运费模板ID
                        freight_name: selectedTemplate.template_name,
                        freight_price: selectedTemplate.freight_price // 记录该组商品使用的完整运费价格
                    });
                }
            }

            // 校验商品库存和价格
            const goodsModel = this.model('goods');
            const { checkPudList, goodsTotalPrice } = await this.service('goods')
                ._checkGoodsAndCalcTotal(allGoodsList, goodsModel, userAgent);

            let actualPrice = goodsTotalPrice + totalFreightPrice;

            // 优惠券处理
            let couponDiscount = 0;
            let couponInfo = null;

            if (couponCode) {
                const couponService = this.service('coupon');
                const validationResult = await couponService.validateCoupon(couponCode, userId, actualPrice);

                if (!validationResult.valid) {
                    return this.fail(validationResult.message);
                }

                couponDiscount = validationResult.discountAmount;
                couponInfo = validationResult.coupon;
                actualPrice = Math.max(0, actualPrice - couponDiscount);
            }

            // 构建打印信息
            let print_info = '';
            for (let i = 0; i < checkPudList.length; i++) {
                const item = checkPudList[i];
                print_info += `${i + 1}、${item.product_name || item.goods_name}【型号：${item.goods_specifition_name}】【数量：${item.buy_number}】 `;
            }

            // 更新订单信息
            const updateOrderInfo = {
                consignee: address.name,
                mobile: address.mobile,
                province: address.province_id,
                city: address.city_id,
                district: address.district_id,
                address: address.address,
                freight_price: totalFreightPrice,
                postscript: postscript,
                goods_price: goodsTotalPrice,
                order_price: goodsTotalPrice + totalFreightPrice,
                actual_price: actualPrice,
                change_price: actualPrice,
                print_info: print_info,
                offline_pay: offlinePay,
                order_type: orderType,
                address_id: addressId,
                remark: remark,
                coupon_code: couponCode,
                coupon_discount: couponDiscount,
                coupon_template_id: think.isEmpty(couponInfo) ? 0 : couponInfo.coupon_template_id
            };

            await this.transaction("order", async (session) => {
                // 更新订单基本信息
                await orderModel.db(session).where({ id: orderId }).update(updateOrderInfo);

                // 删除原有订单商品
                await orderGoodsModel.db(session).where({ order_id: orderId }).update({ is_delete: 1 });

                // 插入新的订单商品
                await orderGoodsModel.db(session).addMany(allOrderGoodsData);

                // // 处理优惠券变更
                // if (existingOrder.coupon_code && existingOrder.coupon_code !== couponCode) {
                //     // 如果原来有优惠券且现在更换了，需要释放原优惠券
                //     const couponService = this.service('coupon');
                //     await couponService.releaseCoupon(existingOrder.coupon_code, userId);
                // }

                // // 使用新的优惠券
                // if (couponCode && couponCode !== existingOrder.coupon_code) {
                //     const couponService = this.service('coupon');
                //     const useResult = await couponService.useCoupon(couponCode, userId, orderId);
                //     if (!useResult) {
                //         throw new Error('优惠券使用失败');
                //     }
                // }
            });

            // 返回更新后的订单信息
            return this.success({
                id: orderId,
                order_sn: existingOrder.order_sn,
                actual_price: actualPrice,
                freight_price: totalFreightPrice,
                goods_price: goodsTotalPrice,
                coupon_discount: couponDiscount,
                coupon_info: couponInfo ? {
                    name: couponInfo.template_name,
                    type: couponInfo.type,
                    discount: couponInfo.discount
                } : null,
                updated: true
            });

        } catch (error) {
            think.logger.error('更新订单失败:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            return this.fail(error.message || '订单更新失败');
        }
    }
};